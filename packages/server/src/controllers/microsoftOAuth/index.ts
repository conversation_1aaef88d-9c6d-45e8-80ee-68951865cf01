import { Request, Response, NextFunction } from 'express'
import { StatusCodes } from 'http-status-codes'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import microsoftOAuthService from '../../services/microsoftOAuth'

// Exchange authorization code for access token
const exchangeCode = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log('🔐 [Microsoft OAuth Controller]: Received code exchange request')
    
    const { code, redirectUri } = req.body

    if (!code) {
      console.error('🔐 [Microsoft OAuth Controller]: Missing authorization code in request')
      throw new InternalFlowiseError(StatusCodes.BAD_REQUEST, 'Authorization code is required')
    }

    if (!redirectUri) {
      console.error('🔐 [Microsoft OAuth Controller]: Missing redirect URI in request')
      throw new InternalFlowiseError(StatusCodes.BAD_REQUEST, 'Redirect URI is required')
    }

    console.log('🔐 [Microsoft OAuth Controller]: Processing code exchange', {
      codeLength: code.length,
      codePreview: code.substring(0, 20) + '...',
      redirectUri
    })

    const result = await microsoftOAuthService.exchangeCode(code, redirectUri)

    console.log('🔐 [Microsoft OAuth Controller]: Code exchange successful', {
      hasAccessToken: !!result.accessToken,
      tokenLength: result.accessToken?.length,
      hasUserInfo: !!result.userInfo
    })

    return res.status(StatusCodes.OK).json({
      accessToken: result.accessToken,
      userInfo: result.userInfo
    })
  } catch (error: any) {
    console.error('🔐 [Microsoft OAuth Controller]: Code exchange failed', {
      error: error.message,
      statusCode: error.statusCode
    })
    next(error)
  }
}

// Login with Microsoft OAuth
const loginWithMicrosoft = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log('🔐 [Microsoft OAuth Controller]: Received Microsoft OAuth login request')
    
    const { accessToken } = req.body

    if (!accessToken) {
      console.error('🔐 [Microsoft OAuth Controller]: Missing access token in request')
      throw new InternalFlowiseError(StatusCodes.BAD_REQUEST, 'Access token is required')
    }

    console.log('🔐 [Microsoft OAuth Controller]: Processing Microsoft OAuth login', {
      tokenLength: accessToken.length,
      tokenPreview: accessToken.substring(0, 20) + '...'
    })

    const result = await microsoftOAuthService.loginWithMicrosoft(accessToken)

    console.log('🔐 [Microsoft OAuth Controller]: Microsoft OAuth login successful', {
      userId: result.user.id,
      username: result.user.username,
      hasAccessToken: !!result.accessToken,
      hasRefreshToken: !!result.refreshToken
    })

    return res.status(StatusCodes.OK).json({
      user: {
        id: result.user.id,
        username: result.user.username,
        email: result.user.email,
        role: result.user.role,
        groupname: result.user.groupname,
        active: result.user.active
      },
      accessToken: result.accessToken,
      refreshToken: result.refreshToken
    })
  } catch (error: any) {
    console.error('🔐 [Microsoft OAuth Controller]: Microsoft OAuth login failed', {
      error: error.message,
      statusCode: error.statusCode
    })
    next(error)
  }
}

export default {
  loginWithMicrosoft,
  exchangeCode
}
