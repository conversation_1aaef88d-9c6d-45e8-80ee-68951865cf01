import { useState, useEffect, useRef } from 'react'
import { PublicClientApplication, InteractionStatus } from '@azure/msal-browser'
import microsoftOAuthDebugger from '@/utils/microsoftOAuthDebug'

// Singleton MSAL instance
let globalMsalInstance = null
let globalInitializationPromise = null

const useMSAL = () => {
  const [msalInstance, setMsalInstance] = useState(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [error, setError] = useState(null)
  const [isInteractionInProgress, setIsInteractionInProgress] = useState(false)
  const interactionInProgressRef = useRef(false)

  useEffect(() => {
    const initializeMSAL = async () => {
      try {
        // If already initialized globally, use the existing instance
        if (globalMsalInstance && globalInitializationPromise) {
          microsoftOAuthDebugger.info('Using existing global MSAL instance')
          await globalInitializationPromise
          setMsalInstance(globalMsalInstance)
          setIsInitialized(true)
          setError(null)
          return
        }

        // MSAL Configuration
        const clientId = '627f8a73-606b-46b2-b72a-621402ddc719'
        if (!clientId || clientId === 'your-microsoft-client-id') {
          throw new Error('Microsoft OAuth is not configured. Please set REACT_APP_MICROSOFT_CLIENT_ID in your .env file.')
        }

        const tenantId = process.env.REACT_APP_MICROSOFT_TENANT_ID || '2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0'
        const msalConfig = {
          auth: {
            clientId: clientId,
            authority: `https://login.microsoftonline.com/${tenantId}`,
            redirectUri: window.location.origin + '/redirect',
            navigateToLoginRequestUrl: false
          },
          cache: {
            cacheLocation: 'sessionStorage',
            storeAuthStateInCookie: false
          },
          system: {
            loggerOptions: {
              loggerCallback: (level, message, containsPii) => {
                if (containsPii) return
                microsoftOAuthDebugger.info(`[MSAL] ${message}`)
              }
            }
          }
        }

        microsoftOAuthDebugger.info('MSAL Configuration', {
          clientId: msalConfig.auth.clientId,
          authority: msalConfig.auth.authority,
          redirectUri: msalConfig.auth.redirectUri,
          currentOrigin: window.location.origin,
          currentPathname: window.location.pathname,
          currentUrl: window.location.href,
          timestamp: new Date().toISOString()
        })
        microsoftOAuthDebugger.info('Creating MSAL instance')
        
        const instance = new PublicClientApplication(msalConfig)
        
        // Store global instance and promise
        globalMsalInstance = instance
        globalInitializationPromise = instance.initialize()
        
        microsoftOAuthDebugger.info('Initializing MSAL instance')
        await globalInitializationPromise
        
        // Check if MSAL is properly configured
        const accounts = instance.getAllAccounts()
        microsoftOAuthDebugger.info('MSAL initialization completed', {
          hasAccounts: accounts.length > 0,
          accountCount: accounts.length,
          timestamp: new Date().toISOString()
        })
        
        microsoftOAuthDebugger.success('MSAL instance initialized successfully')
        
        // Update local state
        setMsalInstance(instance)
        setIsInitialized(true)
        setError(null)

        // Clear any existing interaction state
        try {
          const interactionStatus = await instance.getActiveAccount()
          if (interactionStatus) {
            microsoftOAuthDebugger.info('Found active account, clearing interaction state')
            instance.setActiveAccount(null)
          }
        } catch (clearError) {
          microsoftOAuthDebugger.warning('Failed to clear interaction state during initialization', {
            error: clearError.message,
            timestamp: new Date().toISOString()
          })
        }

        // Only call handleRedirectPromise if we're NOT on the redirect page
        if (!window.location.pathname.includes('/redirect')) {
          // Don't auto-handle on login page to avoid conflicts
          if (window.location.pathname.includes('/login')) {
            microsoftOAuthDebugger.info('Skipping handleRedirectPromise on login page to avoid conflicts')
          } else {
            setIsInteractionInProgress(true)
            interactionInProgressRef.current = true
            await instance.handleRedirectPromise()
            setIsInteractionInProgress(false)
            interactionInProgressRef.current = false
            microsoftOAuthDebugger.info('handleRedirectPromise completed on mount (non-redirect page)')
          }
        } else {
          microsoftOAuthDebugger.info('Skipping handleRedirectPromise on redirect page - will be handled by redirect component')
        }
      } catch (err) {
        // Clear global instance on error
        globalMsalInstance = null
        globalInitializationPromise = null
        
        microsoftOAuthDebugger.error('Failed to initialize MSAL instance', {
          error: err.message,
          errorType: err.constructor.name,
          stack: err.stack
        })
        setError(err)
        setIsInitialized(false)
        setIsInteractionInProgress(false)
        interactionInProgressRef.current = false
        console.error('MSAL initialization failed:', err)
      }
    }

    initializeMSAL()
  }, [])

  const login = async (scopes = ['User.Read']) => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL not initialized')
    }
    
    // Check if there's an interaction in progress
    if (isInteractionInProgress || interactionInProgressRef.current) {
      microsoftOAuthDebugger.warning('Interaction already in progress, loginRedirect blocked')
      throw new Error('Đang có phiên đăng nhập Microsoft khác đang diễn ra. Vui lòng chờ...')
    }
    
    try {
      // Clear any existing interaction state
      microsoftOAuthDebugger.info('Clearing existing interaction state before login')
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
      
      // Clear MSAL cache to reset any pending interactions
      try {
        const accounts = msalInstance.getAllAccounts()
        microsoftOAuthDebugger.info('Found accounts to clear', {
          accountCount: accounts.length,
          accounts: accounts.map(acc => acc.username),
          timestamp: new Date().toISOString()
        })
        
        // Clear all accounts using the correct MSAL API
        if (accounts.length > 0) {
          // Use clearCache method if available, otherwise clear accounts one by one
          if (typeof msalInstance.clearCache === 'function') {
            msalInstance.clearCache()
            microsoftOAuthDebugger.info('Cleared MSAL cache using clearCache method')
          } else {
            // Alternative: clear session storage
            sessionStorage.clear()
            microsoftOAuthDebugger.info('Cleared session storage as fallback')
          }
        }
      } catch (clearError) {
        microsoftOAuthDebugger.warning('Failed to clear MSAL cache', {
          error: clearError.message,
          timestamp: new Date().toISOString()
        })
      }
      
      microsoftOAuthDebugger.info('MSAL cache clearing completed')
      
      const loginRequest = { scopes }
      microsoftOAuthDebugger.info('Initiating Microsoft login redirect', loginRequest)
      setIsInteractionInProgress(true)
      interactionInProgressRef.current = true
      await msalInstance.loginRedirect(loginRequest)
      microsoftOAuthDebugger.success('MSAL login redirect initiated successfully')
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
    } catch (err) {
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
      
      // Handle interaction_in_progress error specifically
      if (err.errorCode === 'interaction_in_progress') {
        microsoftOAuthDebugger.error('Interaction in progress error detected', {
          error: err.message,
          errorCode: err.errorCode,
          timestamp: new Date().toISOString()
        })
        
        // Try to clear the interaction state
        try {
          await msalInstance.handleRedirectPromise()
          microsoftOAuthDebugger.info('Successfully cleared interaction state')
        } catch (clearError) {
          microsoftOAuthDebugger.error('Failed to clear interaction state', {
            error: clearError.message,
            timestamp: new Date().toISOString()
          })
        }
        
        throw new Error('Có phiên đăng nhập Microsoft đang diễn ra. Vui lòng thử lại sau vài giây.')
      }
      
      microsoftOAuthDebugger.error('Error during Microsoft login initiation', {
        error: err.message,
        errorType: err.constructor.name,
        errorCode: err.errorCode
      })
      throw err
    }
  }

  const handleRedirectPromise = async () => {
    if (!isInitialized || !msalInstance) {
      microsoftOAuthDebugger.error('[handleRedirectPromise] MSAL not initialized', {
        isInitialized,
        msalInstanceExists: !!msalInstance,
        timestamp: new Date().toISOString()
      })
      throw new Error('MSAL not initialized')
    }
    try {
      microsoftOAuthDebugger.info('[handleRedirectPromise] Start', {
        url: window.location.href,
        hash: window.location.hash,
        search: window.location.search,
        timestamp: new Date().toISOString()
      })

      setIsInteractionInProgress(true)
      interactionInProgressRef.current = true

      // Log trạng thái trước khi parse URL
      microsoftOAuthDebugger.info('[handleRedirectPromise] Before parsing URL hash', {
        hash: window.location.hash,
        hashLength: window.location.hash.length,
        timestamp: new Date().toISOString()
      })

      // Check if we have authorization code in URL
      const urlParams = new URLSearchParams(window.location.hash.substring(1))
      const hasCode = urlParams.has('code')
      const hasError = urlParams.has('error')
      const hasState = urlParams.has('state')

      microsoftOAuthDebugger.info('[handleRedirectPromise] Parsed URL Parameters', {
        hasCode,
        hasError,
        hasState,
        codeValue: hasCode ? urlParams.get('code').substring(0, 50) + '...' : null,
        errorValue: hasError ? urlParams.get('error') : null,
        stateValue: hasState ? urlParams.get('state').substring(0, 50) + '...' : null,
        allParams: Object.fromEntries(urlParams.entries()),
        timestamp: new Date().toISOString()
      })

      // Log trạng thái MSAL instance
      microsoftOAuthDebugger.info('[handleRedirectPromise] MSAL instance state', {
        accounts: msalInstance.getAllAccounts().map(acc => acc.username),
        activeAccount: msalInstance.getActiveAccount()?.username,
        timestamp: new Date().toISOString()
      })

      // Thực hiện gọi MSAL handleRedirectPromise
      microsoftOAuthDebugger.info('[handleRedirectPromise] Calling msalInstance.handleRedirectPromise...')
      const response = await msalInstance.handleRedirectPromise()
      microsoftOAuthDebugger.info('[handleRedirectPromise] handleRedirectPromise resolved', {
        responseIsNull: response === null,
        responseType: typeof response,
        responseKeys: response ? Object.keys(response) : null,
        timestamp: new Date().toISOString()
      })

      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false

      // Log kết quả trả về
      microsoftOAuthDebugger.info('[handleRedirectPromise] MSAL handleRedirectPromise response', {
        hasResponse: !!response,
        responseType: typeof response,
        responseKeys: response ? Object.keys(response) : null,
        account: response?.account?.username,
        scopes: response?.scopes,
        expiresOn: response?.expiresOn,
        hasAccessToken: !!response?.accessToken,
        tokenType: response?.tokenType,
        fullResponse: response,
        timestamp: new Date().toISOString()
      })

      if (response) {
        microsoftOAuthDebugger.success('[handleRedirectPromise] MSAL redirect response received', {
          account: response.account?.username,
          scopes: response.scopes,
          expiresOn: response.expiresOn,
          hasAccessToken: !!response.accessToken,
          tokenType: response.tokenType
        })

        // Set the active account
        if (response.account) {
          msalInstance.setActiveAccount(response.account)
          microsoftOAuthDebugger.info('[handleRedirectPromise] Active account set', {
            username: response.account.username
          })
        }
      } else {
        // Nếu có code mà không có response, log cảnh báo
        if (hasCode) {
          microsoftOAuthDebugger.error('[handleRedirectPromise] MSAL handleRedirectPromise returned null despite having authorization code', {
            hasCode,
            hasError,
            hasState,
            url: window.location.href,
            hash: window.location.hash,
            timestamp: new Date().toISOString()
          })
        }
        microsoftOAuthDebugger.warning('[handleRedirectPromise] No MSAL redirect response received', {
          url: window.location.href,
          hasUrlParams: !!(window.location.search || window.location.hash),
          hashContent: window.location.hash.substring(0, 100) + '...'
        })
      }
      microsoftOAuthDebugger.info('[handleRedirectPromise] End', {
        timestamp: new Date().toISOString()
      })
      return response
    } catch (err) {
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
      microsoftOAuthDebugger.error('[handleRedirectPromise] Error handling redirect promise', {
        error: err.message,
        errorType: err.constructor.name,
        errorCode: err.errorCode,
        stack: err.stack,
        timestamp: new Date().toISOString()
      })
      throw err
    }
  }

  const clearCache = () => {
    if (msalInstance) {
      try {
        // Use clearCache method if available
        if (typeof msalInstance.clearCache === 'function') {
          msalInstance.clearCache()
          microsoftOAuthDebugger.success('MSAL cache cleared using clearCache method')
        } else {
          // Fallback: clear session storage
          sessionStorage.clear()
          microsoftOAuthDebugger.success('MSAL cache cleared using sessionStorage.clear()')
        }
      } catch (error) {
        microsoftOAuthDebugger.error('Error clearing MSAL cache', error)
      }
    }
  }

  const getAccounts = () => {
    if (!msalInstance) return []
    return msalInstance.getAllAccounts()
  }

  return {
    msalInstance,
    isInitialized,
    error,
    login,
    handleRedirectPromise,
    isInteractionInProgress,
    clearCache,
    getAccounts
  }
}

export default useMSAL
