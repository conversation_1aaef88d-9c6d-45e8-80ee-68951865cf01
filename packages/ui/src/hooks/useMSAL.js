import { useState, useEffect, useRef } from 'react'
import { PublicClientApplication, InteractionStatus } from '@azure/msal-browser'

// Singleton MSAL instance
let globalMsalInstance = null
let globalInitializationPromise = null

const useMSAL = () => {
  const [msalInstance, setMsalInstance] = useState(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [error, setError] = useState(null)
  const [isInteractionInProgress, setIsInteractionInProgress] = useState(false)
  const interactionInProgressRef = useRef(false)

  useEffect(() => {
    const initializeMSAL = async () => {
      try {
        // If already initialized globally, use the existing instance
        if (globalMsalInstance && globalInitializationPromise) {
          await globalInitializationPromise
          setMsalInstance(globalMsalInstance)
          setIsInitialized(true)
          setError(null)
          return
        }

        // MSAL Configuration
        const clientId = '627f8a73-606b-46b2-b72a-621402ddc719'
        if (!clientId || clientId === 'your-microsoft-client-id') {
          throw new Error('Microsoft OAuth is not configured. Please set REACT_APP_MICROSOFT_CLIENT_ID in your .env file.')
        }

        const tenantId = process.env.REACT_APP_MICROSOFT_TENANT_ID || '2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0'
        const msalConfig = {
          auth: {
            clientId: clientId,
            authority: `https://login.microsoftonline.com/${tenantId}`,
            redirectUri: window.location.origin + '/redirect',
            navigateToLoginRequestUrl: false
          },
          cache: {
            cacheLocation: 'sessionStorage',
            storeAuthStateInCookie: false
          },
          system: {
            loggerOptions: {
              loggerCallback: (level, message, containsPii) => {
                if (containsPii) return
                // Only log errors in production
                if (level === 'Error') {
                  console.error(`[MSAL] ${message}`)
                }
              }
            }
          }
        }

        const instance = new PublicClientApplication(msalConfig)

        // Store global instance and promise
        globalMsalInstance = instance
        globalInitializationPromise = instance.initialize()

        await globalInitializationPromise
        
        // Update local state
        setMsalInstance(instance)
        setIsInitialized(true)
        setError(null)

        // Clear any existing interaction state
        try {
          const interactionStatus = await instance.getActiveAccount()
          if (interactionStatus) {
            instance.setActiveAccount(null)
          }
        } catch (clearError) {
          console.error('Failed to clear interaction state during initialization:', clearError)
        }

        // Only call handleRedirectPromise if we're NOT on the redirect page
        if (!window.location.pathname.includes('/redirect')) {
          // Don't auto-handle on login page to avoid conflicts
          if (!window.location.pathname.includes('/login')) {
            setIsInteractionInProgress(true)
            interactionInProgressRef.current = true
            await instance.handleRedirectPromise()
            setIsInteractionInProgress(false)
            interactionInProgressRef.current = false
          }
        }
      } catch (err) {
        // Clear global instance on error
        globalMsalInstance = null
        globalInitializationPromise = null
        
        console.error('Failed to initialize MSAL instance:', err)
        setError(err)
        setIsInitialized(false)
        setIsInteractionInProgress(false)
        interactionInProgressRef.current = false
        console.error('MSAL initialization failed:', err)
      }
    }

    initializeMSAL()
  }, [])

  const login = async (scopes = ['User.Read']) => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL not initialized')
    }
    
    // Check if there's an interaction in progress
    if (isInteractionInProgress || interactionInProgressRef.current) {
      throw new Error('Đang có phiên đăng nhập Microsoft khác đang diễn ra. Vui lòng chờ...')
    }

    try {
      // Clear any existing interaction state
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false

      // Clear MSAL cache to reset any pending interactions
      try {
        const accounts = msalInstance.getAllAccounts()
        if (accounts.length > 0) {
          if (typeof msalInstance.clearCache === 'function') {
            msalInstance.clearCache()
          } else {
            sessionStorage.clear()
          }
        }
      } catch (clearError) {
        console.error('Failed to clear MSAL cache:', clearError)
      }

      const loginRequest = { scopes }
      setIsInteractionInProgress(true)
      interactionInProgressRef.current = true
      await msalInstance.loginRedirect(loginRequest)
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
    } catch (err) {
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
      
      // Handle interaction_in_progress error specifically
      if (err.errorCode === 'interaction_in_progress') {
        // Try to clear the interaction state
        try {
          await msalInstance.handleRedirectPromise()
        } catch (clearError) {
          console.error('Failed to clear interaction state:', clearError)
        }

        throw new Error('Có phiên đăng nhập Microsoft đang diễn ra. Vui lòng thử lại sau vài giây.')
      }

      console.error('Error during Microsoft login initiation:', err)
      throw err
    }
  }

  const handleRedirectPromise = async () => {
    if (!isInitialized || !msalInstance) {
      throw new Error('MSAL not initialized')
    }
    try {
      setIsInteractionInProgress(true)
      interactionInProgressRef.current = true

      // Check if we have authorization code in URL
      const urlParams = new URLSearchParams(window.location.hash.substring(1))
      const hasCode = urlParams.has('code')
      const hasError = urlParams.has('error')

      let response = await msalInstance.handleRedirectPromise()

      // If response is null but we have authorization code, try to handle it manually
      if (!response && hasCode && !hasError) {
        try {
          // Try to acquire token silently first
          const accounts = msalInstance.getAllAccounts()
          if (accounts.length > 0) {
            const silentRequest = {
              scopes: ['User.Read'],
              account: accounts[0]
            }
            response = await msalInstance.acquireTokenSilent(silentRequest)
          }
        } catch (silentError) {
          console.error('Silent token acquisition failed:', silentError)
        }
      }

      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false

      if (response) {
        // Set the active account
        if (response.account) {
          msalInstance.setActiveAccount(response.account)
        }
      }
      return response
    } catch (err) {
      setIsInteractionInProgress(false)
      interactionInProgressRef.current = false
      console.error('Error handling redirect promise:', err)
      throw err
    }
  }

  const clearCache = () => {
    if (msalInstance) {
      try {
        if (typeof msalInstance.clearCache === 'function') {
          msalInstance.clearCache()
        } else {
          sessionStorage.clear()
        }
      } catch (error) {
        console.error('Error clearing MSAL cache:', error)
      }
    }
  }

  const getAccounts = () => {
    if (!msalInstance) return []
    return msalInstance.getAllAccounts()
  }

  return {
    msalInstance,
    isInitialized,
    error,
    login,
    handleRedirectPromise,
    isInteractionInProgress,
    clearCache,
    getAccounts
  }
}

export default useMSAL
