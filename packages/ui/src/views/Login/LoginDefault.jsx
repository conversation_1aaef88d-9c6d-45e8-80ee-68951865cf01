import React, { useState, useEffect } from 'react'
import {
  <PERSON>ton,
  TextField,
  Box,
  Typography,
  Container,
  InputAdornment,
  IconButton,
  CircularProgress,
  Alert,
  Divider
} from '@mui/material'
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined'
import VisibilityOffOutlinedIcon from '@mui/icons-material/VisibilityOffOutlined'
import LoginIcon from '@mui/icons-material/Login'
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline'
import { motion } from 'framer-motion'
import LogoSection from '@/layout/MainLayout/LogoSection'
import userApi from '@/api/user'
import { useDispatch } from 'react-redux'
import { logoutAction } from '@/store/actions'
import MicrosoftLoginButton from '@/components/MicrosoftLoginButton'
import './LoginDefault.css'

const LoginDefault = () => {
  const [showPassword, setShowPassword] = useState(false)
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [msgError, setMsgError] = useState('')
  const dispatch = useDispatch()
  const logout = (...args) => dispatch(logoutAction(...args))

  useEffect(() => {
    // Optional: Add any side effects or initializations here
  }, [])

  const handleLogin = async (e) => {
    e.preventDefault()
    if (!username || !password) {
      setMsgError('Vui lòng nhập tài khoản và mật khẩu.')
      return
    }
    setIsLoading(true)
    setMsgError('')

    try {
      let resData = await userApi.loginUser({ username, password })
      resData = resData.data
      if (resData) {
        localStorage.setItem('dataLogin', JSON.stringify(resData))
        // Clear specified localStorage items after successful login
        localStorage.removeItem('chatInputHistory')
        localStorage.removeItem('57e4146c-dd6b-4eed-ae49-40223b00af25_EXTERNAL')
        localStorage.removeItem('248e6488-33f3-4b91-a75f-a0e1f76f286c_EXTERNAL')

        const redirectUrl = localStorage.getItem('redirectAfterLogin')
        if (redirectUrl) {
          localStorage.removeItem('redirectAfterLogin')
          window.location.href = redirectUrl
        } else {
          window.location.href = '/'
        }
      }
    } catch (error) {
      const errorMsg = error?.response?.data?.message || 'Đã có lỗi xảy ra. Vui lòng thử lại sau.'
      setMsgError(errorMsg)
      localStorage.removeItem('dataLogin')
      logout({})
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className='login-container'>
      <motion.div
        className='login-card'
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box className='logo-section'>
          <LogoSection />
        </Box>
        <Typography component='h1' className='login-title'>
          Chào Mừng Trở Lại
        </Typography>
        <Typography component='p' className='login-subtitle'>
          Đăng nhập để tiếp tục công việc của bạn
        </Typography>

        <form onSubmit={handleLogin} className='login-form' noValidate>
          <TextField
            fullWidth
            label='Tài khoản'
            variant='outlined'
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            error={!!msgError}
            autoComplete='username'
          />
          <TextField
            fullWidth
            label='Mật khẩu'
            type={showPassword ? 'text' : 'password'}
            variant='outlined'
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            error={!!msgError}
            autoComplete='current-password'
            InputProps={{
              endAdornment: (
                <InputAdornment position='end'>
                  <IconButton onClick={() => setShowPassword(!showPassword)} edge='end'>
                    {showPassword ? <VisibilityOffOutlinedIcon /> : <VisibilityOutlinedIcon />}
                  </IconButton>
                </InputAdornment>
              )
            }}
          />

          {msgError && (
            <Alert
              severity='error'
              icon={<ErrorOutlineIcon fontSize='inherit' />}
              className='error-alert'
              sx={{
                width: '100%',
                textAlign: 'left'
              }}
            >
              {msgError}
            </Alert>
          )}

          <Button
            type='submit'
            fullWidth
            variant='contained'
            color='primary'
            className='login-button'
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} color='inherit' /> : <LoginIcon />}
            sx={{
                marginTop: '1.5rem',
            }}
          >
            {isLoading ? 'Đang đăng nhập...' : 'Đăng nhập'}
          </Button>
        </form>

        <Divider className='divider'>HOẶC</Divider>

        <MicrosoftLoginButton
          onError={setMsgError}
          disabled={isLoading}
          fullWidth
        />
      </motion.div>
    </div>
  )
}

export default LoginDefault
