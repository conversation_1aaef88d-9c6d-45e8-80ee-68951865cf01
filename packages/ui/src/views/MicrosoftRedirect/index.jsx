import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { Box, Typography, CircularProgress, Alert } from '@mui/material'
import useMSAL from '../../hooks/useMSAL'
import { loginAction } from '../../store/actions'
import userApi from '../../api/user'

const MicrosoftRedirect = () => {
  const [status, setStatus] = useState('processing')
  const [message, setMessage] = useState('Đang xử lý đăng nhập Microsoft...')
  const [isProcessing, setIsProcessing] = useState(false)

  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { handleRedirectPromise, isInitialized, msalInstance } = useMSAL()

  const login = (...args) => dispatch(loginAction(...args))

  // Main processing effect
  useEffect(() => {
    if (isInitialized && !isProcessing) {
      processRedirect();
    }
  }, [isInitialized]);

  const processRedirect = async () => {
    try {
      setIsProcessing(true)
      setMessage('Đang xử lý thông tin đăng nhập...')

      const result = await handleRedirectPromise()

      if (!result) {
        // Check if we have authorization code in URL as fallback
        const urlParams = new URLSearchParams(window.location.hash.substring(1))
        const authCode = urlParams.get('code')

        if (authCode) {
          try {
            setMessage('Đang xử lý mã xác thực từ Microsoft...')

            // First, exchange authorization code for access token
            const exchangeResponse = await userApi.exchangeMicrosoftCode({
              code: authCode,
              redirectUri: window.location.origin + '/redirect'
            })

            if (exchangeResponse.data && exchangeResponse.data.accessToken) {
              setMessage('Đang hoàn tất đăng nhập...')

              // Now use the access token to login
              const loginResponse = await userApi.loginWithMicrosoft({
                accessToken: exchangeResponse.data.accessToken
              })

              if (loginResponse.data && loginResponse.data.user) {
                // Store login data
                const loginData = {
                  user: loginResponse.data.user,
                  accessToken: loginResponse.data.accessToken,
                  refreshToken: loginResponse.data.refreshToken
                }

                localStorage.setItem('dataLogin', JSON.stringify(loginData))
                window.dispatchEvent(new Event('dataLoginUpdated'))
                login(loginResponse.data.user)

                localStorage.removeItem('microsoftOAuthInProgress')
                setStatus('success')
                setMessage('Đăng nhập thành công!')

                // Get redirect path and navigate
                const redirectAfterLogin = localStorage.getItem('redirectAfterLogin')
                const targetPath = redirectAfterLogin || '/'

                if (redirectAfterLogin) {
                  localStorage.removeItem('redirectAfterLogin')
                }

                setTimeout(() => {
                  window.location.href = targetPath
                }, 500)
                return
              } else {
                throw new Error(loginResponse.data?.message || 'Đăng nhập thất bại')
              }
            } else {
              throw new Error(exchangeResponse.data?.message || 'Không thể xác thực với Microsoft')
            }
          } catch (authCodeError) {
            console.error('Microsoft OAuth fallback failed:', authCodeError)
            // Continue with normal error handling below
          }
        }

        setStatus('error')
        setMessage('Không thể đăng nhập với Microsoft. Vui lòng thử lại.')

        // Clear cached data
        localStorage.removeItem('microsoftOAuthInProgress')
        if (msalInstance && typeof msalInstance.clearCache === 'function') {
          try {
            msalInstance.clearCache()
          } catch (clearError) {
            console.error('Failed to clear MSAL cache:', clearError)
          }
        }

        setTimeout(() => {
          navigate('/login')
        }, 3000)
        return
      }

      // Clear URL hash to prevent MSAL from auto-processing again
      if (window.location.hash) {
        window.history.replaceState(null, '', window.location.pathname)
      }

      const accessToken = result.accessToken
      if (!accessToken) {
        setStatus('error')
        setMessage('Không nhận được access token từ Microsoft.')
        setTimeout(() => {
          navigate('/login')
        }, 3000)
        return
      }

      setMessage('Đang hoàn tất đăng nhập...')

      // Call backend API
      const response = await userApi.loginWithMicrosoft({ accessToken })

      if (response.data && response.data.user) {

        // Store login data
        const loginData = {
          user: response.data.user,
          accessToken: response.data.accessToken,
          refreshToken: response.data.refreshToken
        }

        localStorage.setItem('dataLogin', JSON.stringify(loginData))
        window.dispatchEvent(new Event('dataLoginUpdated'))
        login(response.data.user)
        localStorage.removeItem('microsoftOAuthInProgress')

        setStatus('success')
        setMessage('Đăng nhập thành công!')

        // Get redirect path and navigate
        const redirectAfterLogin = localStorage.getItem('redirectAfterLogin')
        const targetPath = redirectAfterLogin || '/'

        if (redirectAfterLogin) {
          localStorage.removeItem('redirectAfterLogin')
        }

        setTimeout(() => {
          window.location.href = targetPath
        }, 500)
      } else {
        throw new Error(response.data?.message || 'Đăng nhập thất bại')
      }
    } catch (error) {
      console.error('Microsoft OAuth processing failed:', error)
      localStorage.removeItem('microsoftOAuthInProgress')

      setStatus('error')
      setMessage('Đăng nhập Microsoft thất bại. Vui lòng thử lại.')

      setTimeout(() => {
        navigate('/login')
      }, 3000)
    } finally {
      setIsProcessing(false)
    }
  }

  // Add a timeout to handle cases where MSAL doesn't initialize
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (!isInitialized && !isProcessing) {
        localStorage.removeItem('microsoftOAuthInProgress')
        setStatus('error')
        setMessage('Không thể khởi tạo Microsoft OAuth. Vui lòng thử lại.')

        setTimeout(() => {
          navigate('/login')
        }, 3000)
      }
    }, 30000) // 30 seconds timeout

    return () => {
      clearTimeout(timeout)
    }
  }, [isInitialized, isProcessing, navigate])

  // Clear Microsoft OAuth progress flag when component unmounts
  useEffect(() => {
    return () => {
      if (status !== 'success') {
        localStorage.removeItem('microsoftOAuthInProgress')
      }
    }
  }, [status])

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <CircularProgress size={40} sx={{ mb: 2 }} />
      case 'success':
        return <Typography variant="h3" sx={{ mb: 2, color: 'success.main' }}>✓</Typography>
      case 'error':
        return <Typography variant="h3" sx={{ mb: 2, color: 'error.main' }}>✗</Typography>
      default:
        return null
    }
  }

  return (
    <Box
      sx={{
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        p: 3,
        backgroundColor: '#f5f5f5'
      }}
    >
      <Box
        sx={{
          textAlign: 'center',
          backgroundColor: 'white',
          borderRadius: 2,
          p: 4,
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          maxWidth: 500,
          width: '100%'
        }}
      >
        {getStatusIcon()}
        
        <Typography variant="h5" component="h1" gutterBottom>
          Đăng nhập Microsoft
        </Typography>
        
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          {message}
        </Typography>

        {/* Status Alert */}
        {status === 'error' && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {message}
          </Alert>
        )}

        {status === 'success' && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {message}
          </Alert>
        )}


      </Box>
    </Box>
  )
}

export default MicrosoftRedirect