import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { Box, Typography, CircularProgress, Alert } from '@mui/material'
import useMSAL from '../../hooks/useMSAL'
import { loginAction } from '../../store/actions'
import userApi from '../../api/user'
import microsoftOAuthDebugger from '../../utils/microsoftOAuthDebug'

const MicrosoftRedirect = () => {
  const [step, setStep] = useState('initializing')
  const [status, setStatus] = useState('processing')
  const [message, setMessage] = useState('Đang khởi tạo...')
  const [isProcessing, setIsProcessing] = useState(false)

  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { handleRedirectPromise, isInitialized, msalInstance } = useMSAL()

  // Wrap navigate with logging
  const navigateWithLog = (path, reason) => {
    microsoftOAuthDebugger.warning(`🔄 [NAVIGATE] Redirecting to ${path}`, {
      reason,
      step,
      isProcessing,
      isInitialized,
      timestamp: new Date().toISOString()
    })
    navigate(path)
  }

  const login = (...args) => dispatch(loginAction(...args))

  const delayWithLog = async (ms, message, data = null) => {
    microsoftOAuthDebugger.info(`⏳ ${message} - Waiting ${ms}ms`, data)
    await new Promise(resolve => setTimeout(resolve, ms))
  }

  const getStepDescription = (currentStep) => {
    const stepDescriptions = {
      'initializing': 'Khởi tạo MSAL...',
      'validating_token': 'Xác thực token...',
      'calling_backend': 'Gọi API backend...',
      'creating_session': 'Tạo session...',
      'success': 'Hoàn thành!',
      'error_handling': 'Xử lý lỗi...',
      'timeout': 'Hết thời gian chờ...'
    }
    return stepDescriptions[currentStep] || 'Đang xử lý...'
  }

  // Main processing effect
  useEffect(() => {
    if (isInitialized && !isProcessing) {
      processRedirect();
    }
  }, [isInitialized]);

  const processRedirect = async () => {
    try {
      setIsProcessing(true)
      microsoftOAuthDebugger.info('🔄 [PROCESSING] Set isProcessing to true', {
        step,
        timestamp: new Date().toISOString()
      })

      microsoftOAuthDebugger.info('🚀 [STEP 1] Starting Microsoft OAuth redirect processing', {
        url: window.location.href,
        isInitialized,
        hasMSALInstance: !!msalInstance,
        timestamp: new Date().toISOString()
      })

      microsoftOAuthDebugger.info('🔄 [STEP 2] MSAL initialized, processing redirect', {
        isInitialized,
        isProcessing,
        timestamp: new Date().toISOString()
      })

      // Add delay to show MSAL initialization step
      await delayWithLog(2000, '[STEP 2.1] Showing MSAL initialization step')

      setStep('processing_redirect')
      setIsProcessing(true)

      microsoftOAuthDebugger.info('🔄 [STEP 3] Calling handleRedirectPromise', {
        url: window.location.href,
        hasHash: !!window.location.hash,
        timestamp: new Date().toISOString()
      })

      // Add delay to show redirect promise step
      await delayWithLog(2000, '[STEP 3.1] Showing redirect promise step')

      const result = await msalInstance.handleRedirectPromise()
      console.log('result',result)
      // Add delay to show result processing step
      await delayWithLog(2000, '[STEP 3.2] Showing result processing step')

      microsoftOAuthDebugger.info('🔄 [STEP 4] handleRedirectPromise completed', {
        hasResult: !!result,
        resultType: typeof result,
        resultKeys: result ? Object.keys(result) : null,
        resultAccount: result?.account?.username,
        resultAccessToken: result?.accessToken ? 'present' : 'missing',
        resultScopes: result?.scopes,
        timestamp: new Date().toISOString()
      })

      if (!result) {
        microsoftOAuthDebugger.warning('⚠️ [STEP 4] No MSAL result received', {
          result: result,
          resultType: typeof result,
          timestamp: new Date().toISOString()
        })

        setStatus('error')
        setMessage('Không tìm thấy kết quả đăng nhập từ Microsoft. Vui lòng thử lại.')

        // Clear any cached data and redirect to login
        localStorage.removeItem('microsoftOAuthInProgress')
        if (msalInstance) {
          try {
            // Use clearCache method if available
            if (typeof msalInstance.clearCache === 'function') {
              msalInstance.clearCache()
              microsoftOAuthDebugger.info('Cleared MSAL cache using clearCache method')
            } else {
              // Fallback: clear session storage
              sessionStorage.clear()
              microsoftOAuthDebugger.info('Cleared session storage as fallback')
            }
          } catch (clearError) {
            microsoftOAuthDebugger.warning('Failed to clear MSAL cache', {
              error: clearError.message,
              timestamp: new Date().toISOString()
            })
          }
        }

        await delayWithLog(5000, '[STEP 4] Redirecting to login page due to no result')
        navigateWithLog('/login', 'no_result')
        return
      }

      microsoftOAuthDebugger.success('✅ [STEP 5] Microsoft redirect result successfully received', {
        account: result.account?.username,
        hasAccessToken: !!result.accessToken,
        timestamp: new Date().toISOString()
      })

      // Add delay to show success step
      await delayWithLog(3000, '[STEP 5.1] Showing success step')

      // Clear URL hash to prevent MSAL from auto-processing again
      if (window.location.hash) {
        microsoftOAuthDebugger.info('🧹 [CLEANUP] Clearing URL hash to prevent auto-processing', {
          hashLength: window.location.hash.length,
          timestamp: new Date().toISOString()
        })
        window.history.replaceState(null, '', window.location.pathname)
      }

      const accessToken = result.accessToken
      if (!accessToken) {
        setStatus('error')
        setMessage('Không nhận được access token từ Microsoft.')
        await delayWithLog(5000, '[STEP 6] Redirecting to login due to missing access token')
        navigateWithLog('/login', 'missing_access_token')
        return
      }

      setStep('calling_backend')
      microsoftOAuthDebugger.info('🔄 [STEP 7] Calling backend API with access token', {
        hasAccessToken: !!accessToken,
        tokenLength: accessToken.length,
        timestamp: new Date().toISOString()
      })

      // Add delay to show backend calling step
      await delayWithLog(3000, '[STEP 7.1] Showing backend calling step')

      // Call backend API
      const response = await userApi.loginWithMicrosoft({ accessToken })

      if (response.data && response.data.user) {
        setStep('creating_session')
        microsoftOAuthDebugger.success('✅ [STEP 8] Backend authentication successful', {
          userId: response.data.user.id,
          username: response.data.user.username,
          timestamp: new Date().toISOString()
        })

        // Add delay to show backend success
        await delayWithLog(3000, '[STEP 8.1] Showing backend success')

        // Store login data
        const loginData = {
          user: response.data.user,
          accessToken: response.data.accessToken,
          refreshToken: response.data.refreshToken
        }

        localStorage.setItem('dataLogin', JSON.stringify(loginData))
        login(response.data.user)

        // Clear Microsoft OAuth progress flag
        localStorage.removeItem('microsoftOAuthInProgress')

        setStep('success')
        setStatus('success')
        setMessage('Đăng nhập thành công! Đang chuyển hướng...')

        await delayWithLog(5000, '[STEP 9] Preparing final redirect after successful login')
        navigate('/')
      } else {
        throw new Error(response.data?.message || 'Backend authentication failed')
      }
    } catch (error) {
      setStep('error_handling')
      microsoftOAuthDebugger.error('💥 [ERROR] Microsoft redirect processing failed', {
        step: step,
        error: error.message,
        timestamp: new Date().toISOString()
      })

      // Clear Microsoft OAuth progress flag on error
      localStorage.removeItem('microsoftOAuthInProgress')

      setStatus('error')
      setMessage('Đăng nhập Microsoft thất bại. Vui lòng thử lại.')
      
      await delayWithLog(8000, '[ERROR] Redirecting to login page after error')
      navigateWithLog('/login', 'general_error')
    } finally {
      setIsProcessing(false)
      microsoftOAuthDebugger.info('🔄 [PROCESSING] Set isProcessing to false', {
        step,
        timestamp: new Date().toISOString()
      })
    }
  }

  // Handle case when MSAL is not initialized
  useEffect(() => {
    if (!isInitialized && !isProcessing) {
      microsoftOAuthDebugger.info('⏳ [WAITING] MSAL not initialized, waiting for initialization...', {
        isInitialized,
        isProcessing,
        step,
        timestamp: new Date().toISOString()
      })
    }
  }, [isInitialized, isProcessing, step])

  // Add a timeout to handle cases where MSAL doesn't initialize
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (!isInitialized && !isProcessing) {
        setStep('timeout')
        microsoftOAuthDebugger.error('⏰ [TIMEOUT] MSAL initialization timeout after 30 seconds', {
          isInitialized,
          isProcessing,
          step,
          timestamp: new Date().toISOString()
        })
        
        // Clear Microsoft OAuth progress flag on timeout
        localStorage.removeItem('microsoftOAuthInProgress')
        
        setStatus('error')
        setMessage('Không thể khởi tạo Microsoft OAuth. Vui lòng thử lại.')
        
        setTimeout(() => {
          microsoftOAuthDebugger.info('🔄 [TIMEOUT] Redirecting to login page due to timeout')
          navigateWithLog('/login', 'timeout')
        }, 50000) // Increased timeout redirect delay
      } else {
        microsoftOAuthDebugger.info('⏰ [TIMEOUT] Timeout check skipped - processing in progress or MSAL initialized', {
          isInitialized,
          isProcessing,
          step,
          timestamp: new Date().toISOString()
        })
      }
    }, 300000) // Increased timeout to 30 seconds

    return () => {
      clearTimeout(timeout)
      microsoftOAuthDebugger.info('🧹 [TIMEOUT] Timeout cleared', {
        isInitialized,
        isProcessing,
        step,
        timestamp: new Date().toISOString()
      })
    }
  }, [isInitialized, navigateWithLog, step, isProcessing])

  // Clear Microsoft OAuth progress flag when component unmounts
  useEffect(() => {
    return () => {
      // Only clear if we're not in a successful state
      if (status !== 'success') {
        localStorage.removeItem('microsoftOAuthInProgress')
        microsoftOAuthDebugger.info('🧹 [CLEANUP] Component unmounting - cleared microsoftOAuthInProgress flag')
      }
    }
  }, [status])

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <CircularProgress size={40} sx={{ mb: 2 }} />
      case 'success':
        return <Typography variant="h3" sx={{ mb: 2, color: 'success.main' }}>✓</Typography>
      case 'error':
        return <Typography variant="h3" sx={{ mb: 2, color: 'error.main' }}>✗</Typography>
      default:
        return null
    }
  }

  return (
    <Box
      sx={{
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        p: 3,
        backgroundColor: '#f5f5f5'
      }}
    >
      <Box
        sx={{
          textAlign: 'center',
          backgroundColor: 'white',
          borderRadius: 2,
          p: 4,
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          maxWidth: 500,
          width: '100%'
        }}
      >
        {getStatusIcon()}
        
        <Typography variant="h5" component="h1" gutterBottom>
          Đăng nhập Microsoft
        </Typography>
        
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          {message}
        </Typography>

        {/* Step Indicator */}
        <Box sx={{ mb: 3, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Bước hiện tại:
          </Typography>
          <Typography variant="body1" fontWeight="medium" color="primary">
            {getStepDescription(step)}
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
            Step: {step}
          </Typography>
        </Box>

        {/* Status Alert */}
        {status === 'error' && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {message}
          </Alert>
        )}
        
        {status === 'success' && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {message}
          </Alert>
        )}

        {/* Debug Info (only in development) */}
        {process.env.NODE_ENV === 'development' && (
          <Box sx={{ mt: 3, p: 2, backgroundColor: '#f0f0f0', borderRadius: 1, fontSize: '0.75rem' }}>
            <Typography variant="caption" color="text.secondary">
              Debug Info:
            </Typography>
            <Box component="pre" sx={{ mt: 1, fontSize: '0.7rem', overflow: 'auto' }}>
              {JSON.stringify({
                step,
                status,
                isInitialized,
                isProcessing,
                url: window.location.href,
                timestamp: new Date().toISOString()
              }, null, 2)}
            </Box>
          </Box>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <Box sx={{ mt: 2, p: 2, backgroundColor: '#e3f2fd', borderRadius: 1, border: '1px solid #2196f3' }}>
            <Typography variant="body2" color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <CircularProgress size={16} />
              Đang xử lý
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Step: {step} | MSAL: {isInitialized ? 'Ready' : 'Initializing'} | Processing: {isProcessing ? 'Yes' : 'No'}
            </Typography>
          </Box>
        )}

        {/* Waiting for MSAL Status */}
        {!isInitialized && !isProcessing && (
          <Box sx={{ mt: 2, p: 2, backgroundColor: '#fff3e0', borderRadius: 1, border: '1px solid #ff9800' }}>
            <Typography variant="body2" color="warning.main" sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              ⏳ Đang chờ MSAL khởi tạo...
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Timeout sẽ trigger sau 15 giây nếu MSAL không khởi tạo được
          </Typography>
          </Box>
        )}
      </Box>
    </Box>
  )
}

export default MicrosoftRedirect