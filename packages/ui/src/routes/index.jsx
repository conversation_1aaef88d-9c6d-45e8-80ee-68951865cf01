import { useNavigate, useRoutes } from 'react-router-dom'

// routes
import userApi from '@/api/user'
import useApi from '@/hooks/useApi'
import { loginAction } from '@/store/actions'
import { useCallback, useEffect, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useLocation } from 'react-router-dom'
import AdminRoutes from './AdminRoutes'
import CanvasRoutes from './CanvasRoutes'
import ChatbotRoutes from './ChatbotRoutes'
import ChatRoutes from './ChatRoutes'
import LoginRoute from './LoginRoute'
import MainRoutes from './MainRoutes'
import RouteProtection from '@/components/RouteProtection'

// ==============================|| ROUTING RENDER ||============================== //
const whiteList = ['/chatbot/', '/login', '/redirect/']

export default function ThemeRoutes() {
  const { pathname } = useLocation()
  const navigate = useNavigate()

  const user = useSelector((state) => state.user)
  const isLogin = user?.id ? true : false

  const dispatch = useDispatch()
  const login = (...args) => dispatch(loginAction(...args))
  const getUserById = useApi(userApi.getUserById)
  const dataLogin = useMemo(() => (localStorage.getItem('dataLogin') ? JSON?.parse(localStorage.getItem('dataLogin')) : {}), [])

  const handleGetUserById = useCallback(
    async (id) => {
      const resData = await getUserById.request(id)
      if (resData) {
        localStorage.setItem('dataLogin', JSON.stringify({ ...dataLogin, user: resData }))
        login(resData)
      }
    },
    [getUserById, dataLogin]
  )

  useEffect(() => {
    if (dataLogin?.user?.id) {
      handleGetUserById(dataLogin.user.id)
      login(dataLogin.user)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataLogin])

  useEffect(() => {
    // Kiểm tra nếu đang trong quá trình Microsoft OAuth thì không redirect
    const microsoftOAuthInProgress = localStorage.getItem('microsoftOAuthInProgress')
    const isMicrosoftRedirect = pathname === '/redirect'
    
    console.log('🔍 [Routes]: Checking redirect conditions', {
      pathname,
      isMicrosoftRedirect,
      microsoftOAuthInProgress,
      isLogin,
      hasDataLogin: !!dataLogin?.user?.id,
      timestamp: new Date().toISOString()
    })
    
    // Nếu đang ở trang redirect hoặc đang trong quá trình Microsoft OAuth, không redirect
    if (isMicrosoftRedirect || microsoftOAuthInProgress) {
      console.log('🔍 [Routes]: Skipping redirect check - Microsoft OAuth in progress', {
        pathname,
        isMicrosoftRedirect,
        microsoftOAuthInProgress,
        timestamp: new Date().toISOString()
      })
      return
    }
    
    // Chỉ thực hiện redirect check nếu không phải Microsoft OAuth
    if (!whiteList.some((item) => pathname.includes(item)) && !isLogin && !dataLogin?.user?.id) {
      const from = window.location.pathname + window.location.search
      if (from !== '/login') {
        localStorage.setItem('redirectAfterLogin', from)
      }
      console.log('🔍 [Routes]: Redirecting to login page', {
        pathname,
        isLogin,
        hasDataLogin: !!dataLogin?.user?.id,
        timestamp: new Date().toISOString()
      })
      navigate('/login')
    }
  }, [pathname, dataLogin, isLogin, navigate])

  return <RouteProtection>{useRoutes([MainRoutes, CanvasRoutes, ChatbotRoutes, AdminRoutes, LoginRoute, ChatRoutes])}</RouteProtection>
}
