/**
 * Utility functions for Microsoft OAuth cache management
 */

/**
 * Clear all Microsoft OAuth related cache and state
 * This should be called on logout to prevent PKCE issues
 */
export const clearMicrosoftOAuthCache = () => {
  try {
    // Clear localStorage items
    localStorage.removeItem('microsoftOAuthInProgress')
    localStorage.removeItem('dataLogin')
    
    // Clear all MSAL related items from localStorage
    Object.keys(localStorage).forEach(key => {
      if (key.includes('msal') || key.includes('microsoft') || key.includes('Microsoft')) {
        localStorage.removeItem(key)
      }
    })

    // Clear all MSAL related items from sessionStorage
    Object.keys(sessionStorage).forEach(key => {
      if (key.includes('msal') || key.includes('microsoft') || key.includes('Microsoft')) {
        sessionStorage.removeItem(key)
      }
    })

    // Clear URL hash if present to prevent state conflicts
    if (window.location.hash) {
      window.history.replaceState(null, '', window.location.pathname)
    }

    console.log('Microsoft OAuth cache cleared successfully')
  } catch (error) {
    console.error('Error clearing Microsoft OAuth cache:', error)
  }
}

/**
 * Clear MSAL instance cache if available
 * @param {Object} msalInstance - The MSAL instance
 */
export const clearMSALInstanceCache = (msalInstance) => {
  if (!msalInstance) return

  try {
    // Remove all accounts
    const accounts = msalInstance.getAllAccounts()
    accounts.forEach(account => {
      msalInstance.removeAccount(account)
    })

    // Clear MSAL cache
    if (typeof msalInstance.clearCache === 'function') {
      msalInstance.clearCache()
    }

    console.log('MSAL instance cache cleared successfully')
  } catch (error) {
    console.error('Error clearing MSAL instance cache:', error)
  }
}

/**
 * Complete Microsoft OAuth logout
 * Clears all cache and redirects to login
 * @param {Function} logoutAction - Redux logout action
 * @param {Function} navigate - React Router navigate function
 * @param {Object} msalInstance - Optional MSAL instance
 */
export const performMicrosoftOAuthLogout = (logoutAction, navigate, msalInstance = null) => {
  try {
    // Clear MSAL instance cache if provided
    if (msalInstance) {
      clearMSALInstanceCache(msalInstance)
    }

    // Clear all Microsoft OAuth cache
    clearMicrosoftOAuthCache()

    // Perform Redux logout
    if (logoutAction) {
      logoutAction({})
    }

    // Navigate to login
    if (navigate) {
      navigate('/login')
    }

    console.log('Microsoft OAuth logout completed successfully')
  } catch (error) {
    console.error('Error during Microsoft OAuth logout:', error)
    // Still try to navigate to login even if cache clearing fails
    if (navigate) {
      navigate('/login')
    }
  }
}
