// Microsoft OAuth2 Simple Logger
class MicrosoftOAuthDebugger {
  constructor() {
    this.isEnabled = process.env.NODE_ENV === 'development'
  }

  log(message, data = null, level = 'info') {
    if (!this.isEnabled) return

    const emoji = {
      info: '🔍',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    }

    const consoleMethod = level === 'error' ? 'error' : level === 'warning' ? 'warn' : 'log'
    const logMessage = `${emoji[level]} [Microsoft OAuth]: ${message}`

    if (data) {
      console[consoleMethod](logMessage, data)
    } else {
      console[consoleMethod](logMessage)
    }
  }

  info(message, data = null) {
    this.log(message, data, 'info')
  }

  success(message, data = null) {
    this.log(message, data, 'success')
  }

  warning(message, data = null) {
    this.log(message, data, 'warning')
  }

  error(message, data = null) {
    this.log(message, data, 'error')
  }

  // Thêm method để debug redirect logic
  debugRedirectLogic(pathname, isLogin, dataLogin, microsoftOAuthInProgress) {
    console.log('🔍 [Microsoft OAuth Debug]: Redirect Logic Check', {
      pathname,
      isLogin,
      hasDataLogin: !!dataLogin?.user?.id,
      microsoftOAuthInProgress,
      shouldRedirect: !whiteList.some((item) => pathname.includes(item)) && !isLogin && !dataLogin?.user?.id && !microsoftOAuthInProgress
    })
  }

  // Thêm method để debug URL parameters
  debugUrlParameters() {
    const urlParams = new URLSearchParams(window.location.search)
    const hashParams = new URLSearchParams(window.location.hash.substring(1))
    
    console.log('🔍 [Microsoft OAuth Debug]: URL Parameters', {
      url: window.location.href,
      search: window.location.search,
      hash: window.location.hash,
      urlParams: Object.fromEntries(urlParams.entries()),
      hashParams: Object.fromEntries(hashParams.entries()),
      hasCode: urlParams.has('code') || hashParams.has('code'),
      hasAccessToken: urlParams.has('access_token') || hashParams.has('access_token'),
      hasError: urlParams.has('error') || hashParams.has('error')
    })
  }
}

// Create global instance
const microsoftOAuthDebugger = new MicrosoftOAuthDebugger()

export default microsoftOAuthDebugger
