import config from '@/config'
import { baseURL } from '@/store/constant'
import axios from 'axios'

const dataLogin = localStorage.getItem('dataLogin') ? JSON?.parse(localStorage.getItem('dataLogin')) : {}
const accessToken = dataLogin?.accessToken || ''

const apiClient = axios.create({
  baseURL: `${baseURL}/api/v1`,
  headers: {
    'Content-type': 'application/json',
    ...(accessToken && { Authorization: `Bearer ${accessToken}` })
  }
})

apiClient.interceptors.request.use(function (config) {
  return config
})

apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.data && error.response.data.error === 'Unauthorized Access') {
      // Kiểm tra nếu đang trong quá trình Microsoft OAuth thì không redirect
      const microsoftOAuthInProgress = localStorage.getItem('microsoftOAuthInProgress')
      const isMicrosoftRedirect = window.location.pathname === '/redirect'
      
      console.log('🔍 [Axios Interceptor]: Unauthorized error detected', {
        microsoftOAuthInProgress,
        isMicrosoftRedirect,
        pathname: window.location.pathname,
        timestamp: new Date().toISOString()
      })
      
      if (!microsoftOAuthInProgress && !isMicrosoftRedirect) {
        const dataUser = localStorage.getItem('dataLogin')
        if (dataUser) {
          localStorage.removeItem('dataLogin')
          console.log('🔍 [Axios Interceptor]: Redirecting to login due to unauthorized access')
          window.location.href = '/login'
        }
      } else {
        console.log('🔍 [Axios Interceptor]: Skipping redirect - Microsoft OAuth in progress')
      }
    }
    return Promise.reject(error)
  }
)

export default apiClient
