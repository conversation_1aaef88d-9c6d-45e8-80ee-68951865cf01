import client from './client'

const loginUser = (body) => client.post(`/user/login`, body)

const loginWithMicrosoft = (body) => {
  return client.post(`/microsoft-oauth/login`, body)
}

const exchangeMicrosoftCode = (body) => {
  return client.post(`/microsoft-oauth/exchange`, body)
}

const registerUser = (body) => client.post(`/admin/register`, body)

const getUserById = (id) => client.get(`/admin/${id}`)

export const getAllGroupUsers = (search) => client.get(`/admin/group-users?search=${search}`)

const addGroupUser = (body) => client.post(`/admin/group-users/add`, body)

const deleteGroupUser = (idGroupname) => client.delete(`/admin/group-users/delete?idGroupname=${idGroupname}`)

const getUsersByGroup = (groupname) => client.get(`/admin/group-users/group?groupname=${groupname}`)

const getAllUsersGroupedByGroupname = () => client.get(`/admin/group-users/all`)

const removeUser = (id) => client.delete(`/admin/remove-user?id=${id}`)

const updateUser = (id, body) => client.patch(`/admin/update-user?id=${id}`, body)

const getAllDocumentsByUser = (userIds, userGroups, chatflows, limit = 100, offset = 0) =>
  client.get(`/admin/documents?userIds=${userIds}&groups=${userGroups}&chatflows=${chatflows}&limit=${limit}&offset=${offset}`)

const updateDocumentByUser = (body) => client.patch(`/admin/update-document`, body)

const getGroupById = (id) => client.get(`/admin/group/${id}`)

const updateGroupUser = (body) => client.patch(`/admin/update-group-user`, body)

export const getUsers = (limit, username) => client.get(`/admin/users/get-all?limit=${limit}&username=${username}`)

export default {
  getUserById,
  loginUser,
  loginWithMicrosoft,
  exchangeMicrosoftCode,
  registerUser,
  getAllGroupUsers,
  addGroupUser,
  deleteGroupUser,
  getUsersByGroup,
  getAllUsersGroupedByGroupname,
  removeUser,
  updateUser,
  getAllDocumentsByUser,
  updateDocumentByUser,
  getGroupById,
  updateGroupUser,
  getUsers
}
